#!/usr/bin/env python3
"""
🚀 基于Spark的分布式JSONL文件抽样工具
支持TB级数据处理，具备容错和弹性扩展能力

特性：
1. 分布式处理 - 支持多机集群
2. 内存计算 - 高性能数据处理
3. 智能分区 - 优化数据分布
4. 容错机制 - 自动故障恢复
5. 动态扩展 - 弹性资源管理
"""

import os
import sys
import time
import argparse
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from pathlib import Path

try:
    from pyspark.sql import SparkSession, DataFrame
    from pyspark.sql.functions import (
        col, rand, count, collect_list, 
        monotonically_increasing_id, row_number, 
        when, isnan, isnull, size, explode
    )
    from pyspark.sql.window import Window
    from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType
    from pyspark import SparkContext, SparkConf
    SPARK_AVAILABLE = True
except ImportError:
    SPARK_AVAILABLE = False
    print("❌ PySpark未安装，请运行: pip install pyspark")
    sys.exit(1)

# 停止可能存在的SparkSession
try:
    SparkSession.builder.master("local[*]").getOrCreate().stop()
except:
    pass

class SparkSamplerConfig:
    """Spark采样器配置"""
    
    def __init__(self):
        # Spark基础配置
        self.app_name = "DistributedJSONLSampler"
        self.master = "local[*]"  # 默认本地模式

        # 性能优化配置 (大幅增加内存以处理超大文件)
        self.executor_memory = "200g"
        self.driver_memory = "1000g"
        self.executor_cores = "1"
        self.max_result_size = "500g"
        
        # 分区和缓存配置
        self.default_parallelism = None  # 自动检测
        self.sql_adaptive_enabled = True
        self.sql_adaptive_coalesce_partitions_enabled = True
        
        # 序列化配置
        self.serializer = "org.apache.spark.serializer.KryoSerializer"
        self.kryo_unsafe = True
        
        # 动态分配配置 (为大文件处理优化)
        self.dynamic_allocation_enabled = False  # 禁用动态分配以避免内存碎片
        self.dynamic_allocation_min_executors = 1
        self.dynamic_allocation_max_executors = 4  # 减少executor数量，增加单个executor内存
        
    def to_spark_conf(self) -> SparkConf:
        """转换为Spark配置"""
        conf = SparkConf()
        conf.setAppName(self.app_name)
        conf.setMaster(self.master)
        
        # 内存配置
        conf.set("spark.executor.memory", self.executor_memory)
        conf.set("spark.driver.memory", self.driver_memory)
        conf.set("spark.executor.cores", self.executor_cores)
        conf.set("spark.driver.maxResultSize", self.max_result_size)
        
        # 性能优化
        if self.default_parallelism:
            conf.set("spark.default.parallelism", str(self.default_parallelism))
        conf.set("spark.sql.adaptive.enabled", str(self.sql_adaptive_enabled).lower())
        conf.set("spark.sql.adaptive.coalescePartitions.enabled", 
                str(self.sql_adaptive_coalesce_partitions_enabled).lower())
        
        # 序列化优化
        conf.set("spark.serializer", self.serializer)
        conf.set("spark.kryo.unsafe", str(self.kryo_unsafe).lower())
        
        # 动态分配
        conf.set("spark.dynamicAllocation.enabled", str(self.dynamic_allocation_enabled).lower())
        conf.set("spark.dynamicAllocation.minExecutors", str(self.dynamic_allocation_min_executors))
        conf.set("spark.dynamicAllocation.maxExecutors", str(self.dynamic_allocation_max_executors))
        
        # 其他优化 (兼容PySpark 4.0.0)
        try:
            conf.set("spark.sql.execution.arrow.pyspark.enabled", "true")
            conf.set("spark.sql.execution.arrow.maxRecordsPerBatch", "10000")
        except Exception:
            # PySpark 4.0.0可能不支持某些Arrow配置
            pass

        # 添加兼容性配置
        conf.set("spark.sql.adaptive.enabled", "true")
        conf.set("spark.sql.adaptive.coalescePartitions.enabled", "true")
        conf.set("spark.sql.adaptive.skewJoin.enabled", "true")

        # 设置更保守的内存配置
        conf.set("spark.sql.execution.arrow.pyspark.fallback.enabled", "true")

        # 内存优化配置 (Spark 4.0兼容)
        # 注意: spark.executor.memoryFraction 和 spark.storage.memoryFraction
        # 在Spark 1.6+中已被统一内存管理器取代，不再需要手动设置

        # 自适应查询执行配置 (Spark 4.0兼容)
        # 注意: spark.sql.adaptive.coalescePartitions.minPartitionNum 在Spark 4.0中已弃用
        # 使用 spark.sql.adaptive.advisoryPartitionSizeInBytes 来控制分区大小
        conf.set("spark.sql.adaptive.advisoryPartitionSizeInBytes", "128MB")

        # 设置初始分区数 (如果支持的话)
        try:
            conf.set("spark.sql.adaptive.coalescePartitions.initialPartitionNum", "1")
        except Exception:
            # 如果不支持此配置，则跳过
            pass

        # GC和内存优化 (移除Xmx设置，使用Spark内存配置)
        executor_jvm_opts = [
            "-XX:+UseG1GC",
            "-XX:+UnlockDiagnosticVMOptions",
            "-XX:G1HeapRegionSize=32m",
            "-XX:+G1UseAdaptiveIHOP",
            "-XX:G1MixedGCCountTarget=8",
            "-XX:InitiatingHeapOccupancyPercent=35",
            "-XX:G1MixedGCLiveThresholdPercent=85",
            "-XX:G1HeapWastePercent=5",
            "-XX:G1OldCSetRegionThresholdPercent=10",
            "-XX:+UseStringDeduplication",
            "-XX:MaxMetaspaceSize=512m"
            # 注意: 不能在这里设置 -Xmx，必须使用 spark.executor.memory
        ]

        driver_jvm_opts = [
            "-XX:+UseG1GC",
            "-XX:+UnlockDiagnosticVMOptions",
            "-XX:G1HeapRegionSize=16m",
            "-XX:InitiatingHeapOccupancyPercent=35",
            "-XX:+UseStringDeduplication",
            "-XX:MaxMetaspaceSize=256m"
            # 注意: 不能在这里设置 -Xmx，必须使用 spark.driver.memory
        ]

        conf.set("spark.executor.extraJavaOptions", " ".join(executor_jvm_opts))
        conf.set("spark.driver.extraJavaOptions", " ".join(driver_jvm_opts))

        # 网络和序列化优化
        conf.set("spark.network.timeout", "1200s")  # 增加超时时间
        conf.set("spark.sql.broadcastTimeout", "1200s")
        conf.set("spark.rpc.askTimeout", "600s")
        conf.set("spark.rpc.lookupTimeout", "600s")

        # 大文件处理的额外优化
        conf.set("spark.sql.files.maxPartitionBytes", "256MB")  # 增加分区大小
        conf.set("spark.sql.files.openCostInBytes", "8MB")     # 减少小文件开销
        conf.set("spark.sql.adaptive.maxShuffledHashJoinLocalMapThreshold", "0")  # 禁用本地hash join
        conf.set("spark.sql.adaptive.localShuffleReader.enabled", "false")  # 禁用本地shuffle读取

        # 内存压力缓解
        conf.set("spark.sql.execution.arrow.maxRecordsPerBatch", "1000")  # 减少批次大小
        conf.set("spark.sql.inMemoryColumnarStorage.batchSize", "1000")   # 减少列存储批次
        conf.set("spark.sql.windowExec.buffer.spill.threshold", "1000")   # 降低溢写阈值

        # 禁用可能消耗大量内存的功能
        conf.set("spark.sql.adaptive.skewJoin.enabled", "false")  # 禁用倾斜连接优化
        conf.set("spark.sql.adaptive.localShuffleReader.enabled", "false")
        conf.set("spark.sql.adaptive.optimizeSkewsInRebalancePartitions.enabled", "false")

        return conf


class SparkPerformanceMonitor:
    """Spark性能监控器"""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        self.start_time = time.time()
        self.checkpoints = {}
        
    def checkpoint(self, name: str):
        """记录检查点"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # 获取Spark指标
        sc = self.spark.sparkContext
        status = sc.statusTracker()
        
        # 兼容PySpark 4.0.0的API变化
        try:
            if hasattr(status, 'getActiveJobIds'):
                active_jobs = len(status.getActiveJobIds())
            elif hasattr(status, 'getActiveJobsIds'):
                active_jobs = len(status.getActiveJobsIds())
            else:
                active_jobs = 0

            if hasattr(status, 'getActiveStageIds'):
                active_stages = len(status.getActiveStageIds())
            elif hasattr(status, 'getActiveStagesIds'):
                active_stages = len(status.getActiveStagesIds())
            else:
                active_stages = 0

            if hasattr(status, 'getExecutorInfos'):
                executor_infos = len(status.getExecutorInfos())
            else:
                executor_infos = 0
        except Exception:
            active_jobs = 0
            active_stages = 0
            executor_infos = 0

        self.checkpoints[name] = {
            'time': current_time,
            'elapsed': elapsed,
            'active_jobs': active_jobs,
            'active_stages': active_stages,
            'executor_infos': executor_infos
        }
        
    def get_cluster_info(self) -> Dict:
        """获取集群信息"""
        sc = self.spark.sparkContext
        status = sc.statusTracker()

        # 兼容PySpark 4.0.0的API变化
        try:
            if hasattr(status, 'getExecutorInfos'):
                executor_count = len(status.getExecutorInfos())
            else:
                executor_count = 1  # 默认值
        except Exception:
            executor_count = 1

        return {
            'application_id': sc.applicationId,
            'application_name': sc.appName,
            'master': sc.master,
            'executor_count': executor_count,
            'default_parallelism': sc.defaultParallelism,
            'spark_version': sc.version
        }
        
    def print_summary(self):
        """打印性能摘要"""
        total_time = time.time() - self.start_time
        cluster_info = self.get_cluster_info()
        
        print(f"\n{'='*60}")
        print("🚀 Spark性能分析报告")
        print(f"{'='*60}")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🖥️  集群信息:")
        print(f"   - 应用ID: {cluster_info['application_id']}")
        print(f"   - 执行器数量: {cluster_info['executor_count']}")
        print(f"   - 并行度: {cluster_info['default_parallelism']}")
        print(f"   - Spark版本: {cluster_info['spark_version']}")
        
        if len(self.checkpoints) > 1:
            print(f"\n📊 各阶段耗时:")
            prev_time = self.start_time
            for name, data in self.checkpoints.items():
                stage_time = data['time'] - prev_time
                percentage = (stage_time / total_time) * 100
                print(f"  - {name}: {stage_time:.2f}s ({percentage:.1f}%)")
                prev_time = data['time']


class DistributedJSONLSampler:
    """分布式JSONL采样器"""
    
    def __init__(self, config: SparkSamplerConfig):
        self.config = config
        self.spark = None
        self.monitor = None
        
    def __enter__(self):
        """上下文管理器入口"""
        try:
            # 设置Java环境变量
            import os
            if 'JAVA_HOME' not in os.environ:
                # 尝试自动检测Java路径 (PySpark 4.0.0需要Java 17+)
                java_paths = [
                    '/opt/homebrew/opt/openjdk@17',  # 推荐版本
                    '/opt/homebrew/opt/openjdk@21',
                    '/opt/homebrew/opt/openjdk@11',
                    '/opt/homebrew/opt/openjdk@8',
                    '/usr/lib/jvm/java-17-openjdk',
                    '/usr/lib/jvm/java-11-openjdk',
                    '/usr/lib/jvm/java-8-openjdk'
                ]
                for java_path in java_paths:
                    if os.path.exists(java_path):
                        os.environ['JAVA_HOME'] = java_path
                        print(f"🔧 自动设置JAVA_HOME: {java_path}")
                        break

            # 创建Spark会话，增加错误处理
            self.spark = SparkSession.builder.config(conf=self.config.to_spark_conf()).getOrCreate()
            self.monitor = SparkPerformanceMonitor(self.spark)

            # 设置日志级别
            self.spark.sparkContext.setLogLevel("WARN")

            print(f"✅ Spark会话已启动")
            print(f"   - 应用名称: {self.config.app_name}")
            print(f"   - 主节点: {self.config.master}")
            print(f"   - 执行器内存: {self.config.executor_memory}")
            print(f"   - Java版本: {os.environ.get('JAVA_HOME', '未设置')}")

        except Exception as e:
            print(f"❌ Spark会话启动失败: {e}")
            print("💡 请检查:")
            print("   1. Java是否正确安装 (java -version)")
            print("   2. JAVA_HOME环境变量是否设置")
            print("   3. PySpark版本是否兼容")
            raise

        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        if self.spark:
            self.spark.stop()
            print("✅ Spark会话已关闭")
            
    def read_jsonl_files(self, file_pattern: str) -> DataFrame:
        """读取JSONL文件"""
        self.monitor.checkpoint("开始读取文件")

        print(f"📁 读取文件: {file_pattern}")

        try:
            df = self.spark.read \
                .json(file_pattern)

        except Exception as e:
            raise ValueError(f"读取文件失败: {e}")

        # 检查数据
        total_count = df.count()
        print(f"📊 总记录数: {total_count:,}")

        if total_count == 0:
            raise ValueError("读取的数据为空")

        # 检查source字段
        if 'source' not in df.columns:
            raise ValueError("数据中缺少'source'字段")

        # 显示source分布（限制显示数量以避免内存问题）
        # source_dist = df.groupBy('source').count().orderBy('count', ascending=False).limit(20)
        # print(f"📈 Source分布 (前20个):")
        # for row in source_dist.collect():
        #     print(f"   - {row['source']}: {row['count']:,} 条")

        self.monitor.checkpoint("文件读取完成")
        return df
        
    def load_previous_records(self, record_files: List[str]) -> Dict[str, List[int]]:
        """加载历史采样记录"""
        if not record_files:
            return {}
            
        print("📚 加载历史采样记录...")
        sampled_indices = {}
        
        for record_file in record_files:
            if not os.path.exists(record_file):
                continue
                
            try:
                with open(record_file, 'r', encoding='utf-8') as f:
                    record_data = json.load(f)
                    
                if 'source_details' in record_data:
                    for source, indices in record_data['source_details'].items():
                        if source not in sampled_indices:
                            sampled_indices[source] = []
                        sampled_indices[source].extend(indices)
                        
            except Exception as e:
                print(f"⚠️ 记录文件 {record_file} 加载失败: {e}")
                
        if sampled_indices:
            total_previous = sum(len(indices) for indices in sampled_indices.values())
            print(f"📋 加载了 {total_previous:,} 条历史采样记录")
            
        return sampled_indices
        
    def filter_previous_samples(self, df: DataFrame, previous_records: Dict[str, List[int]]) -> DataFrame:
        """过滤已采样的数据"""
        if not previous_records:
            return df

        self.monitor.checkpoint("开始过滤历史数据")

        # 检查历史记录数量，如果太多则跳过过滤以避免内存问题
        total_previous = sum(len(indices) for indices in previous_records.values())
        if total_previous > 5000000000:  # 降低到5万条以减少内存压力
            print(f"⚠️ 历史记录过多 ({total_previous:,} 条)，跳过过滤以避免内存问题")
            print("   建议: 清理历史记录文件或使用更小的采样比例")
            return df

        print(f"🔍 过滤 {total_previous:,} 条历史记录...")

        # 分批处理每个source以减少内存使用
        filtered_dfs = []

        for source in df.select('source').distinct().collect():
            source_name = source['source']
            source_df = df.filter(col('source') == source_name)

            if source_name in previous_records and previous_records[source_name]:
                # 添加行号用于过滤
                window = Window.orderBy(monotonically_increasing_id())
                source_df_with_id = source_df.withColumn('row_id', row_number().over(window) - 1)

                # 过滤已采样的记录
                indices_to_exclude = previous_records[source_name]
                # 分批处理大的索引列表 (降低阈值以减少内存使用)
                if len(indices_to_exclude) > 5000000000:  # 降低到5000条
                    print(f"⚠️ {source_name} 的历史记录过多 ({len(indices_to_exclude)} 条)，跳过过滤")
                    filtered_source_df = source_df
                else:
                    filtered_source_df = source_df_with_id.filter(~col('row_id').isin(indices_to_exclude)).drop('row_id')
            else:
                filtered_source_df = source_df

            filtered_dfs.append(filtered_source_df)

        # 合并所有过滤后的DataFrame
        if filtered_dfs:
            df_filtered = filtered_dfs[0]
            for df_part in filtered_dfs[1:]:
                df_filtered = df_filtered.union(df_part)
        else:
            df_filtered = df.limit(0)  # 空DataFrame

        remaining_count = df_filtered.count()
        print(f"📊 过滤后剩余: {remaining_count:,} 条记录")

        self.monitor.checkpoint("历史数据过滤完成")
        return df_filtered
        
    def stratified_sample(self, df: DataFrame, sample_ratio: float, random_seed: int = 42) -> Tuple[DataFrame, Dict]:
        """分层采样 (内存优化版本)"""
        self.monitor.checkpoint("开始分层采样")

        print(f"🎯 开始分层采样 (比例: {sample_ratio*100:.1f}%)")

        # 计算每个source的采样数量 (使用更高效的方式)
        print("📊 统计各source数据量...")
        source_counts = df.groupBy('source').count().collect()
        sampling_plan = {}

        print(f"📋 采样计划:")
        for row in source_counts:
            source = row['source']
            total_count = row['count']
            sample_count = max(1, int(total_count * sample_ratio))
            sample_count = min(sample_count, total_count)
            sampling_plan[source] = sample_count
            print(f"   - {source}: {total_count:,} -> {sample_count:,}")

        # 内存优化的分层采样
        sampled_dfs = []
        sample_details = {}

        print("🔄 执行分层采样...")
        for i, (source, sample_count) in enumerate(sampling_plan.items()):
            if sample_count > 0:
                print(f"   处理source {i+1}/{len(sampling_plan)}: {source}")

                # 过滤并缓存source数据
                source_df = df.filter(col('source') == source).cache()
                total_count = source_df.count()

                if total_count > 0:
                    # 使用更保守的采样策略
                    ratio = min(1.0, (sample_count * 1.2) / total_count)  # 多采样20%以确保足够数据
                    sampled_source_df = source_df.sample(fraction=ratio, seed=random_seed)

                    # 限制到目标数量
                    sampled_source_df = sampled_source_df.limit(sample_count)

                    # 立即缓存采样结果
                    sampled_source_df = sampled_source_df.cache()
                    actual_count = sampled_source_df.count()

                    print(f"     实际采样: {actual_count:,} 条")

                    sampled_dfs.append(sampled_source_df)
                    sample_details[source] = actual_count

                # 清理source数据缓存
                source_df.unpersist()

        # 合并所有采样结果 (分批合并以减少内存压力)
        print("🔗 合并采样结果...")
        if sampled_dfs:
            # 分批合并DataFrame
            batch_size = 3  # 每次合并3个DataFrame
            current_df = sampled_dfs[0]

            for i in range(1, len(sampled_dfs), batch_size):
                batch = sampled_dfs[i:i + batch_size]
                for df_part in batch:
                    current_df = current_df.union(df_part)

                # 定期重新分区以优化性能
                if i % (batch_size * 2) == 0:
                    current_df = current_df.coalesce(4)

            sampled_df = current_df
        else:
            sampled_df = df.limit(0)  # 空DataFrame

        # 清理中间结果
        for df_part in sampled_dfs:
            df_part.unpersist()

        total_sampled = sampled_df.count()
        print(f"✅ 采样完成，共 {total_sampled:,} 条数据")

        self.monitor.checkpoint("分层采样完成")
        return sampled_df, sample_details
        
    def write_results(self, sampled_df: DataFrame, output_prefix: str, 
                     max_lines_per_file: int = 10000) -> List[str]:
        """写入采样结果"""
        self.monitor.checkpoint("开始写入结果")
        
        total_count = sampled_df.count()
        num_partitions = max(1, (total_count + max_lines_per_file - 1) // max_lines_per_file)
        
        print(f"💾 写入 {total_count:,} 条记录到 {num_partitions} 个分区")
        
        # 重新分区以控制输出文件数量
        output_df = sampled_df.coalesce(num_partitions)
        
        # 写入JSON文件
        output_path = f"{output_prefix}_spark"
        output_df.write.mode('overwrite').option("compression", "none").json(output_path)
        
        # 获取输出文件列表
        output_files = []
        output_dir = Path(output_path)
        if output_dir.exists():
            for file_path in output_dir.glob("part-*.json"):
                # 重命名文件
                new_name = f"{output_prefix}_part{len(output_files)+1:03d}.jsonl"
                file_path.rename(file_path.parent.parent / new_name)
                output_files.append(new_name)
                
        print(f"✅ 结果已写入 {len(output_files)} 个文件")
        
        self.monitor.checkpoint("结果写入完成")
        return output_files
        
    def save_sampling_record(self, sample_details: Dict, output_files: List[str], 
                           record_file: str, sample_ratio: float, random_seed: int):
        """保存采样记录"""
        record_data = {
            'timestamp': datetime.now().isoformat(),
            'sample_ratio': sample_ratio,
            'random_seed': random_seed,
            'total_sampled': sum(sample_details.values()),
            'output_files': output_files,
            'source_details': sample_details,
            'spark_config': {
                'app_name': self.config.app_name,
                'master': self.config.master,
                'executor_memory': self.config.executor_memory,
                'driver_memory': self.config.driver_memory
            }
        }
        
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(record_data, f, ensure_ascii=False, indent=2)
            
        print(f"📄 采样记录已保存到: {record_file}")


def main():
    parser = argparse.ArgumentParser(description='🚀 Spark分布式JSONL文件采样工具')
    parser.add_argument('--input', '-i', default='*.jsonl',
                        help='输入文件匹配模式 (默认: *.jsonl)')
    parser.add_argument('--output', '-o', default='sampled_data',
                        help='输出文件前缀 (默认: sampled_data)')
    parser.add_argument('--ratio', '-r', type=float, default=0.1,
                        help='抽样比例 (默认: 0.1 即10%%)')
    parser.add_argument('--seed', '-s', type=int, default=42,
                        help='随机种子 (默认: 42)')
    parser.add_argument('--record', default='sampling_record_spark.json',
                        help='本次索引记录文件名')
    parser.add_argument('--previous-records', nargs='*', default=[],
                        help='之前的采样记录文件路径列表')
    parser.add_argument('--max-lines', type=int, default=10000,
                        help='每个输出文件的最大行数 (默认: 10,000)')
    
    # Spark配置参数
    parser.add_argument('--master', default='local[*]',
                        help='Spark主节点 (默认: local[*])')
    parser.add_argument('--executor-memory', default='200g',
                        help='执行器内存 (默认: 4g)')
    parser.add_argument('--driver-memory', default='1000g',
                        help='驱动器内存 (默认: 2g)')
    parser.add_argument('--executor-cores', default='2',
                        help='执行器核心数 (默认: 2)')
    
    args = parser.parse_args()
    
    if not 0 < args.ratio <= 1.0:
        print("❌ 错误: 抽样比例必须在 0 和 1 之间")
        return
        
    # 创建Spark配置
    config = SparkSamplerConfig()
    config.master = args.master
    config.executor_memory = args.executor_memory
    config.driver_memory = args.driver_memory
    config.executor_cores = args.executor_cores
    
    start_time = time.time()
    
    try:
        with DistributedJSONLSampler(config) as sampler:
            # 读取数据
            print("=" * 60)
            print("🚀 第一步: 分布式数据读取")
            print("=" * 60)
            df = sampler.read_jsonl_files(args.input)
            
            # 加载历史记录
            print("\n" + "=" * 60)
            print("📚 第二步: 加载历史记录")
            print("=" * 60)
            previous_records = sampler.load_previous_records(args.previous_records)
            
            # 过滤已采样数据
            print("\n" + "=" * 60)
            print("🔍 第三步: 过滤历史数据")
            print("=" * 60)
            filtered_df = sampler.filter_previous_samples(df, previous_records)
            
            # 分层采样
            print("\n" + "=" * 60)
            print("🎯 第四步: 分布式采样")
            print("=" * 60)
            sampled_df, sample_details = sampler.stratified_sample(
                filtered_df, args.ratio, args.seed
            )
            
            # 写入结果
            print("\n" + "=" * 60)
            print("💾 第五步: 分布式写入")
            print("=" * 60)
            output_files = sampler.write_results(sampled_df, args.output, args.max_lines)
            
            # 保存记录
            sampler.save_sampling_record(
                sample_details, output_files, args.record, args.ratio, args.seed
            )
            
            # 性能报告
            total_time = time.time() - start_time
            total_sampled = sum(sample_details.values())
            
            print("\n" + "=" * 60)
            print("🎉 Spark分布式采样完成!")
            print("=" * 60)
            print(f"⏱️  总耗时: {total_time:.2f}秒")
            print(f"🔥 处理速度: {df.count() / total_time:.0f} 条/秒")
            print(f"📊 原始数据: {df.count():,} 条")
            print(f"📈 本次采样: {total_sampled:,} 条")
            print(f"📁 输出文件: {len(output_files)} 个")
            
            # 打印性能摘要
            sampler.monitor.print_summary()
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
