import orjson
import os
import random
import hashlib
from collections import defaultdict, Counter
from pathlib import Path
import argparse
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import pickle
import tempfile
import shutil


class LargeJSONLSampler:
    def __init__(self, input_dir, output_dir, num_splits=10, sample_ratio=1.0, max_workers=None):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.num_splits = num_splits
        self.sample_ratio = sample_ratio
        self.max_workers = max_workers or min(mp.cpu_count(), 8)
        self.output_dir.mkdir(exist_ok=True)

    def step1_analyze_sources(self):
        """第一步：多进程扫描所有文件，统计每个source的数量分布"""
        print("Step 1: 分析数据源分布...")

        jsonl_files = list(self.input_dir.glob("3_4*.jsonl"))
        print(f"发现 {len(jsonl_files)} 个JSONL文件，使用 {self.max_workers} 个进程并行处理")

        source_counts = Counter()
        file_source_mapping = defaultdict(set)

        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_file = {
                executor.submit(analyze_single_file, str(file)): file
                for file in jsonl_files
            }

            for future in as_completed(future_to_file):
                file = future_to_file[future]
                try:
                    file_source_counts, file_sources = future.result()
                    print(f"✓ 完成分析: {file.name} ({len(file_sources)} 个源)")

                    source_counts.update(file_source_counts)
                    file_source_mapping[str(file)] = file_sources

                except Exception as exc:
                    print(f"✗ 分析文件失败 {file}: {exc}")

        print(f"发现 {len(source_counts)} 个不同的数据源")
        print("数据源分布:")
        for source, count in source_counts.most_common():
            print(f"  {source}: {count:,} 条记录")

        return source_counts, file_source_mapping

    def step2_calculate_sampling_strategy(self, source_counts):
        """第二步：计算分层抽样策略"""
        print("\nStep 2: 计算分层抽样策略...")

        total_records = sum(source_counts.values())
        target_total = int(total_records * self.sample_ratio)
        target_per_split = target_total // self.num_splits

        print(f"总记录数: {total_records:,}")
        print(f"目标抽样总数: {target_total:,}")
        print(f"每个分割目标数: {target_per_split:,}")

        source_per_split = {}

        for source, count in source_counts.items():
            source_ratio = count / total_records
            source_target_total = int(target_total * source_ratio)
            source_target_per_split = source_target_total // self.num_splits

            source_per_split[source] = source_target_per_split

            print(f"  {source}: {count:,} -> {source_target_total:,} "
                  f"(每分割: {source_target_per_split:,})")

        return source_per_split, target_per_split

    def step3_create_global_sample_pool(self, file_source_mapping, source_per_split):
        """第三步：创建全局样本池，确保无重复"""
        print("\nStep 3: 创建全局样本池...")

        # 为每个source创建全局样本池
        source_sample_pools = {}

        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # 为每个source并行创建样本池
            future_to_source = {}

            for source in source_per_split.keys():
                if source_per_split[source] > 0:
                    target_samples = source_per_split[source] * self.num_splits
                    future = executor.submit(
                        create_source_sample_pool,
                        source,
                        file_source_mapping,
                        target_samples
                    )
                    future_to_source[future] = source

            # 收集结果
            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    samples = future.result()
                    source_sample_pools[source] = samples
                    print(f"✓ {source}: 创建了 {len(samples):,} 个样本的池")

                except Exception as exc:
                    print(f"✗ 为源 {source} 创建样本池失败: {exc}")

        return source_sample_pools

    def step4_distribute_samples(self, source_sample_pools, source_per_split):
        """第四步：将样本无重复地分配到各个分割"""
        print("\nStep 4: 分配样本到各分割...")

        # 为每个分割创建输出文件
        output_files = []
        for i in range(self.num_splits):
            output_file = self.output_dir / f"split_{i:02d}.jsonl"
            output_files.append(open(output_file, 'w', encoding='utf-8'))

        try:
            split_counts = [defaultdict(int) for _ in range(self.num_splits)]

            for source, sample_pool in source_sample_pools.items():
                target_per_split = source_per_split[source]

                if target_per_split == 0 or not sample_pool:
                    continue

                print(f"分配源 {source} 的样本...")

                # 随机打乱样本池
                shuffled_samples = sample_pool.copy()
                random.shuffle(shuffled_samples)

                # 轮询分配样本到各个分割
                for i, sample in enumerate(shuffled_samples):
                    split_idx = i % self.num_splits

                    # 检查该分割是否已达到目标数量
                    if split_counts[split_idx][source] < target_per_split:
                        output_files[split_idx].write(sample + '\n')
                        split_counts[split_idx][source] += 1
                    else:
                        # 如果当前分割已满，找下一个未满的分割
                        for next_split in range(self.num_splits):
                            if split_counts[next_split][source] < target_per_split:
                                output_files[next_split].write(sample + '\n')
                                split_counts[next_split][source] += 1
                                break

            # 显示分配结果
            for split_idx in range(self.num_splits):
                print(f"分割 {split_idx + 1} 的样本分布:")
                total_in_split = sum(split_counts[split_idx].values())
                print(f"  总计: {total_in_split:,} 条记录")
                for source, count in split_counts[split_idx].items():
                    print(f"    {source}: {count:,}")

        finally:
            for f in output_files:
                f.close()

        print(f"\n样本分配完成！结果保存在: {self.output_dir}")

    def run(self):
        """运行完整的抽样流程"""
        # Step 1: 分析数据源
        source_counts, file_source_mapping = self.step1_analyze_sources()

        # Step 2: 计算抽样策略
        source_per_split, target_per_split = self.step2_calculate_sampling_strategy(source_counts)

        # Step 3: 创建全局样本池
        source_sample_pools = self.step3_create_global_sample_pool(file_source_mapping, source_per_split)

        # Step 4: 分配样本
        self.step4_distribute_samples(source_sample_pools, source_per_split)

        return True


# 多进程辅助函数
def analyze_single_file(file_path):
    """分析单个文件的数据源分布"""
    source_counts = Counter()
    file_sources = set()

    try:
        with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:
            for line_no, line in enumerate(f):
                try:
                    data = orjson.loads(line.strip())
                    source = data.get('source', 'unknown')
                    source_counts[source] += 1
                    file_sources.add(source)
                except orjson.JSONDecodeError:
                    continue
                except Exception:
                    continue

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return Counter(), set()

    return source_counts, file_sources


def create_source_sample_pool(target_source, file_source_mapping, target_samples):
    """为指定source创建样本池"""
    samples = []

    # 收集所有包含该source的文件
    relevant_files = [
        file_path for file_path, sources in file_source_mapping.items()
        if target_source in sources
    ]

    # 使用两阶段reservoir sampling
    # 第一阶段：从每个文件收集样本
    file_samples = []
    for file_path in relevant_files:
        try:
            with open(file_path, 'r', encoding='utf-8', buffering=8192 * 16) as f:
                temp_reservoir = []
                seen_count = 0

                for line in f:
                    try:
                        data = orjson.loads(line.strip())
                        source = data.get('source', 'unknown')

                        if source == target_source:
                            seen_count += 1

                            # Reservoir sampling for this file
                            if len(temp_reservoir) < target_samples // len(relevant_files) + 1000:
                                temp_reservoir.append(line.strip())
                            else:
                                j = random.randint(0, seen_count - 1)
                                if j < len(temp_reservoir):
                                    temp_reservoir[j] = line.strip()

                    except orjson.JSONDecodeError:
                        continue
                    except Exception:
                        continue

                file_samples.extend(temp_reservoir)

        except Exception as e:
            print(f"Error reading {file_path} for source {target_source}: {e}")
            continue

    # 第二阶段：从所有文件样本中最终抽样
    if len(file_samples) <= target_samples:
        samples = file_samples
    else:
        # 再次使用reservoir sampling
        samples = []
        for i, sample in enumerate(file_samples):
            if len(samples) < target_samples:
                samples.append(sample)
            else:
                j = random.randint(0, i)
                if j < target_samples:
                    samples[j] = sample

    # 打乱样本顺序
    random.shuffle(samples)
    return samples


def main():
    parser = argparse.ArgumentParser(description='大规模JSONL文件分层抽样工具（无重复多进程版）')
    parser.add_argument('input_dir', help='输入JSONL文件目录')
    parser.add_argument('output_dir', help='输出目录')
    parser.add_argument('--splits', type=int, default=10, help='分割数量 (默认: 10)')
    parser.add_argument('--ratio', type=float, default=1.0, help='抽样比例 (默认: 1.0, 即全量)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子 (默认: 42)')
    parser.add_argument('--workers', type=int, default=None, help='并行进程数 (默认: min(CPU核心数, 8))')

    args = parser.parse_args()

    # 设置随机种子确保可重复性
    random.seed(args.seed)

    print(f"开始处理（无重复版本）...")
    print(f"输入目录: {args.input_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"分割数量: {args.splits}")
    print(f"抽样比例: {args.ratio}")
    print(f"随机种子: {args.seed}")
    print(f"并行进程数: {args.workers or 'auto'}")
    print("-" * 50)

    sampler = LargeJSONLSampler(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        num_splits=args.splits,
        sample_ratio=args.ratio,
        max_workers=args.workers
    )

    success = sampler.run()

    if success:
        print("\n🎉 处理完成！数据无重复！")

        # 验证无重复（可选）
        print("\n验证结果...")
        verify_no_duplicates(args.output_dir, args.splits)
    else:
        print("\n❌ 处理失败！")


def verify_no_duplicates(output_dir, num_splits):
    """验证分割间无重复数据"""
    all_lines = set()
    duplicate_count = 0

    for i in range(num_splits):
        split_file = Path(output_dir) / f"split_{i:02d}.jsonl"
        if split_file.exists():
            with open(split_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line in all_lines:
                        duplicate_count += 1
                    else:
                        all_lines.add(line)

    if duplicate_count == 0:
        print(f"✓ 验证完成：{len(all_lines):,} 条记录，无重复")
    else:
        print(f"✗ 发现 {duplicate_count:,} 条重复记录")


if __name__ == "__main__":
    main()