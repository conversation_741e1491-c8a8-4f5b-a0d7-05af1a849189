"""
WARNING: Using incubator modules: jdk.incubator.vector
Using Spark's default log4j profile: org/apache/spark/log4j2-defaults.properties
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
25/09/10 07:20:45 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
"""

"""
java heap


25/09/10 09:03:35 ERROR TaskSchedulerImpl: Exception in statusUpdate
java.util.concurrent.RejectedExecutionException: Task org.apache.spark.scheduler.TaskResultGetter$$Lambda$9785/0x0000000101d937c0@41481a2e rejected from java.util.concurrent.ThreadPoolExecutor@7b97287b[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 11175]
        at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2065)
        at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:833)
        at java.base/java.util.concurrent.ThreadPoolExecutor.execute(ThreadPoolExecutor.java:1365)
        at org.apache.spark.scheduler.TaskResultGetter.enqueueFailedTask(TaskResultGetter.scala:140)
        at org.apache.spark.scheduler.TaskSchedulerImpl.liftedTree2$1(TaskSchedulerImpl.scala:813)
        at org.apache.spark.scheduler.TaskSchedulerImpl.statusUpdate(TaskSchedulerImpl.scala:786)
        at org.apache.spark.scheduler.local.LocalEndpoint$$anonfun$receive$1.applyOrElse(LocalSchedulerBackend.scala:73)
        at org.apache.spark.rpc.netty.Inbox.$anonfun$process$1(Inbox.scala:116)
        at org.apache.spark.rpc.netty.Inbox.safelyCall(Inbox.scala:216)
        at org.apache.spark.rpc.netty.Inbox.process(Inbox.scala:101)
        at org.apache.spark.rpc.netty.MessageLoop.org$apache$spark$rpc$netty$MessageLoop$$receiveLoop(MessageLoop.scala:76)
        at org.apache.spark.rpc.netty.MessageLoop$$anon$1.run(MessageLoop.scala:42)
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
        at java.base/java.lang.Thread.run(Thread.java:840)
25/09/10 09:03:35 WARN MemoryStore: Not enough space to cache rdd_11_149 in memory! (computed 4.0 MiB so far)
25/09/10 09:03:35 WARN MemoryStore: Not enough space to cache rdd_11_34 in memory! (computed 4.0 MiB so far)
25/09/10 09:03:35 ERROR TaskSchedulerImpl: Exception in statusUpdate
java.util.concurrent.RejectedExecutionException: Task org.apache.spark.scheduler.TaskResultGetter$$Lambda$9785/0x0000000101d937c0@378cb40 rejected from java.util.concurrent.ThreadPoolExecutor@7b97287b[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 11175]
"""


#!/usr/bin/env python3
"""
用于对1.3TB数据进行10轮分层采样，每轮10%随机采样
"""

import time
import os
from datetime import datetime
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, count, desc

def create_spark_session():
    """创建优化的SparkSession"""
    # 停止现有session
    try:
        SparkSession.builder.master("local[*]").getOrCreate().stop()
    except:
        pass
    
    # 创建SparkSession并设置配置参数
    spark = (SparkSession.builder
             .appName("Chinese-FinWeb-Multi-Round-Sampling")
             # 内存配置 - 增加内存以处理大数据
             .config("spark.driver.memory", "32g")
             .config("spark.driver.maxResultSize", "8g")
             .config("spark.executor.memory", "12g")
             .config("spark.executor.cores", "4")
             # AQE自适应查询执行
             .config("spark.sql.adaptive.enabled", "true")
             .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
             .config("spark.sql.adaptive.skewJoin.enabled", "true")
             .config("spark.sql.adaptive.advisoryPartitionSizeInBytes", "128MB")
             # 序列化和Arrow优化
             .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
             .config("spark.sql.execution.arrow.pyspark.enabled", "true")
             # 文件读取优化
             .config("spark.sql.files.maxPartitionBytes", "134217728")  # 128MB
             .config("spark.sql.shuffle.partitions", "200")
             # 网络和超时配置
             .config("spark.network.timeout", "800s")
             .config("spark.executor.heartbeatInterval", "60s")
             # 内存管理优化
             .config("spark.sql.execution.arrow.maxRecordsPerBatch", "10000")
             .config("spark.serializer.objectStreamReset", "100")
             .getOrCreate())
    
    return spark

def analyze_source_distribution(df, round_num=None):
    """分析source字段分布"""
    prefix = f"[轮次 {round_num}] " if round_num else ""
    print(f"{prefix}📊 分析source分布...")
    
    try:
        source_dist = df.groupBy('source').count().orderBy(desc('count'))
        source_stats = source_dist.collect()
        
        total_records = sum(row['count'] for row in source_stats)
        print(f"{prefix}   总记录数: {total_records:,}")
        print(f"{prefix}   source种类数: {len(source_stats)}")
        
        # 显示前10个source的分布
        print(f"{prefix}   前10个source分布:")
        for i, row in enumerate(source_stats[:10]):
            percentage = (row['count'] / total_records) * 100
            print(f"{prefix}     {i+1}. {row['source']}: {row['count']:,} ({percentage:.2f}%)")
        
        return source_stats, total_records
    except Exception as e:
        print(f"{prefix}❌ 分析source分布失败: {e}")
        return [], 0

def stratified_sample_by_source(df, sample_fraction, seed, round_num):
    """按source进行分层采样"""
    print(f"[轮次 {round_num}] 🎯 开始分层采样 (比例: {sample_fraction*100:.1f}%)")
    
    try:
        # 获取所有source
        sources = [row['source'] for row in df.select('source').distinct().collect()]
        print(f"[轮次 {round_num}]    发现 {len(sources)} 个不同的source")
        
        sampled_dfs = []
        source_sample_stats = {}
        
        for i, source in enumerate(sources):
            print(f"[轮次 {round_num}]    处理source {i+1}/{len(sources)}: {source}")
            
            # 过滤当前source的数据
            source_df = df.filter(col('source') == source)
            source_count = source_df.count()
            
            if source_count > 0:
                # 对当前source进行采样
                sampled_source_df = source_df.sample(
                    fraction=sample_fraction, 
                    seed=seed + i  # 为每个source使用不同的种子
                )
                
                sampled_count = sampled_source_df.count()
                sampled_dfs.append(sampled_source_df)
                source_sample_stats[source] = {
                    'original': source_count,
                    'sampled': sampled_count,
                    'ratio': sampled_count / source_count if source_count > 0 else 0
                }
                
                print(f"[轮次 {round_num}]      {source}: {source_count:,} -> {sampled_count:,} ({sampled_count/source_count*100:.2f}%)")
        
        # 合并所有采样结果
        if sampled_dfs:
            print(f"[轮次 {round_num}]    合并 {len(sampled_dfs)} 个source的采样结果...")
            final_sampled_df = sampled_dfs[0]
            for df_part in sampled_dfs[1:]:
                final_sampled_df = final_sampled_df.union(df_part)
        else:
            final_sampled_df = df.limit(0)  # 空DataFrame
        
        return final_sampled_df, source_sample_stats
        
    except Exception as e:
        print(f"[轮次 {round_num}] ❌ 分层采样失败: {e}")
        return df.limit(0), {}

def save_sampling_results(sampled_df, output_path, round_num):
    """保存采样结果"""
    print(f"[轮次 {round_num}] 💾 保存采样结果到: {output_path}")
    
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存为JSON格式
        sampled_df.write.mode("overwrite").json(output_path)
        print(f"[轮次 {round_num}] ✅ 数据保存完成")
        return True
    except Exception as e:
        print(f"[轮次 {round_num}] ❌ 保存失败: {e}")
        return False

def main():
    """主函数"""
    start_time = time.time()
    
    print("🚀 开始多轮分层采样任务")
    print("=" * 80)
    
    # 创建Spark会话
    spark = create_spark_session()
    print("✅ SparkSession创建成功")
    
    try:
        # 读取数据
        print("\n📁 读取数据...")
        df = spark.read.json("/home/<USER>/datasets/chinese-fineweb-edu-v2.1/3_4*.jsonl")
        
        # 缓存原始数据以提高后续访问速度
        df.cache()
        
        # 分析原始数据
        print("\n📊 分析原始数据...")
        original_source_stats, total_records = analyze_source_distribution(df)
        print(f"原始数据总量: {total_records:,} 条记录")
        
        # 采样配置
        sample_fraction = 0.1
        base_seed = 42
        num_rounds = 10
        
        # 存储所有轮次的统计信息
        all_round_stats = []
        
        print(f"\n🎯 开始 {num_rounds} 轮采样，每轮采样比例: {sample_fraction*100:.1f}%")
        print("=" * 80)
        
        # 执行多轮采样
        for round_num in range(1, num_rounds + 1):
            round_start_time = time.time()
            print(f"\n🔄 第 {round_num} 轮采样开始...")
            
            try:
                # 分层采样
                sampled_df, source_stats = stratified_sample_by_source(
                    df, sample_fraction, base_seed + round_num, round_num
                )
                
                # 统计采样结果
                sampled_count = sampled_df.count()
                
                # 保存结果
                output_path = f"/home/<USER>/datasets/chinese-fineweb-edu-v2.1/sampled/batch_{round_num}/sampled_spark"
                save_success = save_sampling_results(sampled_df, output_path, round_num)
                
                # 计算耗时
                round_time = time.time() - round_start_time
                
                # 记录统计信息
                round_stats = {
                    'round': round_num,
                    'sampled_count': sampled_count,
                    'time_seconds': round_time,
                    'source_stats': source_stats,
                    'save_success': save_success
                }
                all_round_stats.append(round_stats)
                
                # 输出轮次总结
                print(f"[轮次 {round_num}] ✅ 完成 - 采样 {sampled_count:,} 条，耗时 {round_time:.2f}秒")
                
                # 清理内存
                sampled_df.unpersist()
                spark.catalog.clearCache()
                
            except Exception as e:
                print(f"[轮次 {round_num}] ❌ 采样失败: {e}")
                continue
        
        # 输出最终汇总报告
        print("\n" + "=" * 80)
        print("📈 10轮采样汇总报告")
        print("=" * 80)
        
        total_sampled = sum(stats['sampled_count'] for stats in all_round_stats)
        total_time = time.time() - start_time
        successful_rounds = sum(1 for stats in all_round_stats if stats['save_success'])
        
        print(f"✅ 成功完成轮次: {successful_rounds}/{num_rounds}")
        print(f"📊 总采样数据量: {total_sampled:,} 条")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"🚀 平均处理速度: {total_sampled/total_time:.0f} 条/秒")
        
        # 详细轮次统计
        print(f"\n📋 各轮次详细统计:")
        for stats in all_round_stats:
            status = "✅" if stats['save_success'] else "❌"
            print(f"  轮次 {stats['round']}: {status} {stats['sampled_count']:,} 条, {stats['time_seconds']:.2f}秒")
        
        # Source分布汇总
        print(f"\n📊 Source采样分布汇总:")
        all_sources = set()
        for stats in all_round_stats:
            all_sources.update(stats['source_stats'].keys())
        
        for source in sorted(all_sources):
            total_sampled_for_source = sum(
                stats['source_stats'].get(source, {}).get('sampled', 0) 
                for stats in all_round_stats
            )
            print(f"  {source}: 总计采样 {total_sampled_for_source:,} 条")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print(f"\n🧹 清理资源...")
        spark.catalog.clearCache()
        spark.stop()
        print("✅ 程序执行完成")

if __name__ == "__main__":
    main()