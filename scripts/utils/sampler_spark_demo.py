"""
WARNING: Using incubator modules: jdk.incubator.vector
Using Spark's default log4j profile: org/apache/spark/log4j2-defaults.properties
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
25/09/10 07:20:45 WARN NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
"""

"""
java heap


25/09/10 09:03:35 ERROR TaskSchedulerImpl: Exception in statusUpdate
java.util.concurrent.RejectedExecutionException: Task org.apache.spark.scheduler.TaskResultGetter$$Lambda$9785/0x0000000101d937c0@41481a2e rejected from java.util.concurrent.ThreadPoolExecutor@7b97287b[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 11175]
        at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2065)
        at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:833)
        at java.base/java.util.concurrent.ThreadPoolExecutor.execute(ThreadPoolExecutor.java:1365)
        at org.apache.spark.scheduler.TaskResultGetter.enqueueFailedTask(TaskResultGetter.scala:140)
        at org.apache.spark.scheduler.TaskSchedulerImpl.liftedTree2$1(TaskSchedulerImpl.scala:813)
        at org.apache.spark.scheduler.TaskSchedulerImpl.statusUpdate(TaskSchedulerImpl.scala:786)
        at org.apache.spark.scheduler.local.LocalEndpoint$$anonfun$receive$1.applyOrElse(LocalSchedulerBackend.scala:73)
        at org.apache.spark.rpc.netty.Inbox.$anonfun$process$1(Inbox.scala:116)
        at org.apache.spark.rpc.netty.Inbox.safelyCall(Inbox.scala:216)
        at org.apache.spark.rpc.netty.Inbox.process(Inbox.scala:101)
        at org.apache.spark.rpc.netty.MessageLoop.org$apache$spark$rpc$netty$MessageLoop$$receiveLoop(MessageLoop.scala:76)
        at org.apache.spark.rpc.netty.MessageLoop$$anon$1.run(MessageLoop.scala:42)
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
        at java.base/java.lang.Thread.run(Thread.java:840)
25/09/10 09:03:35 WARN MemoryStore: Not enough space to cache rdd_11_149 in memory! (computed 4.0 MiB so far)
25/09/10 09:03:35 WARN MemoryStore: Not enough space to cache rdd_11_34 in memory! (computed 4.0 MiB so far)
25/09/10 09:03:35 ERROR TaskSchedulerImpl: Exception in statusUpdate
java.util.concurrent.RejectedExecutionException: Task org.apache.spark.scheduler.TaskResultGetter$$Lambda$9785/0x0000000101d937c0@378cb40 rejected from java.util.concurrent.ThreadPoolExecutor@7b97287b[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 11175]
"""


#!/usr/bin/env python3
"""
用于对1.3TB数据进行10%随机采样
"""

from pyspark.sql import SparkSession

SparkSession.builder.master("local[*]").getOrCreate().stop()

# 创建SparkSession并设置配置参数
spark = (SparkSession.builder
         .appName("Chinese-FinWeb-Sampling")
         # 内存配置
         .config("spark.driver.memory", "20g")
         .config("spark.driver.maxResultSize", "4g")
         .config("spark.executor.memory", "8g")
         .config("spark.executor.cores", "4")
         # AQE自适应查询执行
         .config("spark.sql.adaptive.enabled", "true")
         .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
         .config("spark.sql.adaptive.skewJoin.enabled", "true")
         .config("spark.sql.adaptive.advisoryPartitionSizeInBytes", "128MB")
         # 序列化和Arrow优化
         .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
         .config("spark.sql.execution.arrow.pyspark.enabled", "true")
         # 文件读取优化
         .config("spark.sql.files.maxPartitionBytes", "134217728")  # 128MB
         .config("spark.sql.shuffle.partitions", "200")
         # 网络和超时配置
         .config("spark.network.timeout", "600s")
         .config("spark.executor.heartbeatInterval", "60s")
         .getOrCreate())

print("SparkSession创建成功")

# 读取数据
print("开始读取数据...")
df = spark.read.json("/home/<USER>/datasets/chinese-fineweb-edu-v2.1/3_4*.jsonl")

# 对读取的数据进行采样，一共 1.3T，每次采样 0.1，随机种子 42
print("开始数据采样...")
sampled_df = df.sample(fraction=0.1, seed=42)

# 查看原始数据量
print("统计原始数据量...")
original_count = df.count()
print(f"原始数据行数: {original_count:,}")

# 查看采样后数据量
print("统计采样数据量...")
sampled_count = sampled_df.count()
print(f"采样后数据行数: {sampled_count:,}")
print(f"采样比例: {sampled_count/original_count:.2%}")

# 查看数据结构
print("\n数据结构:")
sampled_df.printSchema()

# 查看前几行数据
print("\n前5行数据:")
sampled_df.show(5, truncate=False)

# 基本统计信息
print("\n采样数据基本统计:")
sampled_df.describe().show()

# 保存采样后的数据
output_path = "/home/<USER>/datasets/chinese-fineweb-edu-v2.1/sampled/sampled_spark"
print(f"\n保存采样数据到: {output_path}")
sampled_df.write.mode("overwrite").json(output_path)
print("数据保存完成")

# 内存清理
spark.catalog.clearCache()
print("\n处理完成，已清理缓存")

# 关闭SparkSession
# spark.stop()